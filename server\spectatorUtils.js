// Utility functions for spectator mode

/**
 * Add a spectator to a game lobby
 * @param {Object} io - Socket.io instance
 * @param {Object} socket - Socket of the spectator
 * @param {string} gameCode - Game lobby code
 * @param {string} spectatorName - Name of the spectator
 * @param {Map} gameLobbies - Map of game lobbies
 * @param {Map} spectatorToGameLobby - Map of spectator socket IDs to game lobby codes
 * @param {Map} lobbies - Map of regular lobbies (for checking invite codes)
 * @returns {Object} - Result of the operation
 */
function addSpectator(io, socket, gameCode, spectatorName, gameLobbies, spectatorToGameLobby, lobbies) {
  console.log(`Attempting to add spectator ${spectatorName} to game ${gameCode}`);
  console.log(`Available game lobbies: ${Array.from(gameLobbies.keys()).join(', ')}`);

  // First check if the code is a direct game lobby code
  if (!gameLobbies.has(gameCode)) {
    console.log(`Game lobby ${gameCode} not found directly, checking if it's an invite code...`);

    // Check if it's an invite code that redirects to a lobby
    let redirectedLobbyCode = null;

    // Check if it's a partner or opponent invite code
    for (const [lobbyCode, lobby] of lobbies.entries()) {
      if (lobby.partnerInviteCode === gameCode || lobby.opponentInviteCode === gameCode) {
        console.log(`Found invite code ${gameCode} redirecting to lobby ${lobbyCode}`);
        redirectedLobbyCode = lobbyCode;
        break;
      }
    }

    // If we found a redirected lobby code, check if there's a game lobby for it
    if (redirectedLobbyCode && gameLobbies.has(redirectedLobbyCode)) {
      console.log(`Found game lobby ${redirectedLobbyCode} via invite code ${gameCode}`);
      gameCode = redirectedLobbyCode;
    } else if (redirectedLobbyCode && lobbies.has(redirectedLobbyCode)) {
      // Check if the redirected lobby has started a game
      const redirectedLobby = lobbies.get(redirectedLobbyCode);
      if (redirectedLobby.gameStarted) {
        console.log(`Lobby ${redirectedLobbyCode} has started a game, but it's not in gameLobbies yet`);

        // Create a game lobby entry for this lobby
        gameLobbies.set(redirectedLobbyCode, {
          ...redirectedLobby,
          spectators: [],
          originalLobbies: [redirectedLobbyCode]
        });

        console.log(`Created game lobby entry for ${redirectedLobbyCode}`);
        gameCode = redirectedLobbyCode;
      } else {
        console.log(`Redirected lobby ${redirectedLobbyCode} exists but game hasn't started yet`);
        return { success: false, error: 'Game has not started yet' };
      }
    } else {
      // Check if the original code is a direct lobby code
      if (lobbies.has(gameCode) && !lobbies.get(gameCode).redirectTo) {
        const directLobby = lobbies.get(gameCode);
        if (directLobby.gameStarted) {
          console.log(`Lobby ${gameCode} has started a game, but it's not in gameLobbies yet`);

          // Create a game lobby entry for this lobby
          gameLobbies.set(gameCode, {
            ...directLobby,
            spectators: [],
            originalLobbies: [gameCode]
          });

          console.log(`Created game lobby entry for ${gameCode}`);
        } else {
          console.log(`Lobby ${gameCode} exists but game hasn't started yet`);
          return { success: false, error: 'Game has not started yet' };
        }
      } else {
        console.log(`No game lobby found for code ${gameCode} or any redirected lobby`);
        return { success: false, error: 'Game not found' };
      }
    }
  }

  const gameLobby = gameLobbies.get(gameCode);

  // Create spectator object
  const spectator = {
    id: socket.id,
    name: spectatorName,
    isSpectator: true,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${spectatorName}-spectator`
  };

  // Initialize spectators array if it doesn't exist
  if (!gameLobby.spectators) {
    gameLobby.spectators = [];
  }

  // Add spectator to the game lobby
  gameLobby.spectators.push(spectator);

  // Associate spectator with game lobby
  spectatorToGameLobby.set(socket.id, gameCode);

  // Join socket room
  socket.join(gameCode);

  // Notify all players and spectators in the game lobby
  io.to(gameCode).emit('spectator_joined', {
    spectator,
    spectatorCount: gameLobby.spectators.length
  });

  console.log(`Spectator ${spectatorName} (${socket.id}) joined game ${gameCode}`);

  // Return success
  return {
    success: true,
    gameLobby,
    spectator
  };
}

/**
 * Remove a spectator from a game lobby
 * @param {Object} io - Socket.io instance
 * @param {string} spectatorId - Socket ID of the spectator
 * @param {Map} gameLobbies - Map of game lobbies
 * @param {Map} spectatorToGameLobby - Map of spectator socket IDs to game lobby codes
 * @returns {Object} - Result of the operation
 */
function removeSpectator(io, spectatorId, gameLobbies, spectatorToGameLobby) {
  // Check if the spectator is associated with a game lobby
  if (!spectatorToGameLobby.has(spectatorId)) {
    return { success: false, error: 'Spectator not found' };
  }

  const gameCode = spectatorToGameLobby.get(spectatorId);
  const gameLobby = gameLobbies.get(gameCode);

  if (!gameLobby || !gameLobby.spectators) {
    return { success: false, error: 'Game lobby or spectators not found' };
  }

  // Find the spectator in the game lobby
  const spectatorIndex = gameLobby.spectators.findIndex(s => s.id === spectatorId);
  if (spectatorIndex === -1) {
    return { success: false, error: 'Spectator not found in game lobby' };
  }

  // Get spectator info before removing
  const spectator = gameLobby.spectators[spectatorIndex];

  // Remove spectator from the game lobby
  gameLobby.spectators.splice(spectatorIndex, 1);

  // Remove association
  spectatorToGameLobby.delete(spectatorId);

  // Notify all players and spectators in the game lobby
  io.to(gameCode).emit('spectator_left', {
    spectatorId,
    spectatorCount: gameLobby.spectators.length
  });

  console.log(`Spectator ${spectator.name} (${spectatorId}) left game ${gameCode}`);

  // Return success
  return { success: true };
}

/**
 * Get all spectators for a game lobby
 * @param {string} gameCode - Game lobby code
 * @param {Map} gameLobbies - Map of game lobbies
 * @param {Map} lobbies - Map of regular lobbies (for checking invite codes)
 * @returns {Array} - List of spectators
 */
function getSpectators(gameCode, gameLobbies, lobbies) {
  // First check if the code is a direct game lobby code
  if (!gameLobbies.has(gameCode)) {
    // Check if it's an invite code that redirects to a lobby
    let redirectedLobbyCode = null;

    // Check if it's a partner or opponent invite code
    for (const [lobbyCode, lobby] of lobbies.entries()) {
      if (lobby.partnerInviteCode === gameCode || lobby.opponentInviteCode === gameCode) {
        redirectedLobbyCode = lobbyCode;
        break;
      }
    }

    // If we found a redirected lobby code, check if there's a game lobby for it
    if (redirectedLobbyCode && gameLobbies.has(redirectedLobbyCode)) {
      gameCode = redirectedLobbyCode;
    } else if (redirectedLobbyCode && lobbies.has(redirectedLobbyCode)) {
      // Check if the redirected lobby has started a game
      const redirectedLobby = lobbies.get(redirectedLobbyCode);
      if (redirectedLobby.gameStarted) {
        // Create a game lobby entry for this lobby
        gameLobbies.set(redirectedLobbyCode, {
          ...redirectedLobby,
          spectators: [],
          originalLobbies: [redirectedLobbyCode]
        });

        gameCode = redirectedLobbyCode;
      } else {
        return [];
      }
    } else {
      // Check if the original code is a direct lobby code
      if (lobbies.has(gameCode) && !lobbies.get(gameCode).redirectTo) {
        const directLobby = lobbies.get(gameCode);
        if (directLobby.gameStarted) {
          // Create a game lobby entry for this lobby
          gameLobbies.set(gameCode, {
            ...directLobby,
            spectators: [],
            originalLobbies: [gameCode]
          });
        } else {
          return [];
        }
      } else {
        return [];
      }
    }
  }

  const gameLobby = gameLobbies.get(gameCode);
  return gameLobby.spectators || [];
}

/**
 * Send current game state to a spectator
 * @param {Object} socket - Socket of the spectator
 * @param {string} gameCode - Game lobby code
 * @param {Map} gameLobbies - Map of game lobbies
 * @param {Map} lobbies - Map of regular lobbies (for checking invite codes)
 */
function sendGameStateToSpectator(socket, gameCode, gameLobbies, lobbies) {
  // First check if the code is a direct game lobby code
  if (!gameLobbies.has(gameCode)) {
    // Check if it's an invite code that redirects to a lobby
    let redirectedLobbyCode = null;

    // Check if it's a partner or opponent invite code
    for (const [lobbyCode, lobby] of lobbies.entries()) {
      if (lobby.partnerInviteCode === gameCode || lobby.opponentInviteCode === gameCode) {
        redirectedLobbyCode = lobbyCode;
        break;
      }
    }

    // If we found a redirected lobby code, check if there's a game lobby for it
    if (redirectedLobbyCode && gameLobbies.has(redirectedLobbyCode)) {
      gameCode = redirectedLobbyCode;
    } else if (redirectedLobbyCode && lobbies.has(redirectedLobbyCode)) {
      // Check if the redirected lobby has started a game
      const redirectedLobby = lobbies.get(redirectedLobbyCode);
      if (redirectedLobby.gameStarted) {
        // Create a game lobby entry for this lobby
        gameLobbies.set(redirectedLobbyCode, {
          ...redirectedLobby,
          spectators: [],
          originalLobbies: [redirectedLobbyCode]
        });

        gameCode = redirectedLobbyCode;
      } else {
        return { success: false, error: 'Game has not started yet' };
      }
    } else {
      // Check if the original code is a direct lobby code
      if (lobbies.has(gameCode) && !lobbies.get(gameCode).redirectTo) {
        const directLobby = lobbies.get(gameCode);
        if (directLobby.gameStarted) {
          // Create a game lobby entry for this lobby
          gameLobbies.set(gameCode, {
            ...directLobby,
            spectators: [],
            originalLobbies: [gameCode]
          });
        } else {
          return { success: false, error: 'Game has not started yet' };
        }
      } else {
        return { success: false, error: 'Game not found' };
      }
    }
  }

  const gameLobby = gameLobbies.get(gameCode);

  // Create a game state object for the spectator
  // This includes all information that should be visible to a spectator
  const gameState = {
    players: gameLobby.players,
    teams: gameLobby.teams,
    teamNames: gameLobby.teamNames,
    trumpSuit: gameLobby.trumpSuit,
    currentTurn: gameLobby.currentTurn,
    playedCards: gameLobby.playedCards,
    hands: gameLobby.hands,
    balls: gameLobby.balls,
    scores: gameLobby.scores,
    ballScores: gameLobby.ballScores,
    dealerId: gameLobby.dealerId,
    trumpSelectorId: gameLobby.trumpSelectorId,
    // Include all player cards for spectator view
    playerCards: gameLobby.playerCards,
    // Include the current game phase
    gamePhase: gameLobby.gamePhase,
    // Include any other relevant game state
    targetScores: gameLobby.targetScores,
    jordhiCalls: gameLobby.jordhiCalls
  };

  // Send the game state to the spectator
  socket.emit('spectator_game_state', gameState);

  return { success: true };
}

module.exports = {
  addSpectator,
  removeSpectator,
  getSpectators,
  sendGameStateToSpectator
};
