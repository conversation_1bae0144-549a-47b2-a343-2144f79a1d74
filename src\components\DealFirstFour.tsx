"use client";
import { useState, useEffect } from "react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { Button } from "./ui/button";

interface DealFirstFourProps {
  onDealComplete: () => void;
}

export default function DealFirstFour({ onDealComplete }: DealFirstFourProps) {
  const { isDealer, players } = useGameStore();
  const [isDealing, setIsDealing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [dealerCheckComplete, setDealerCheckComplete] = useState(false);

  // Check if the current player is actually the dealer according to the server
  useEffect(() => {
    const checkDealerStatus = async () => {
      try {
        // Get the current player's ID
        const currentPlayerId = socketService.getSocketId();
        console.log("Current player ID:", currentPlayerId);

        // Find the dealer in the players array
        const dealer = players.find(p => p.isDealer);
        console.log("Dealer from players array:", dealer);

        // Log the dealer status for debugging
        console.log("isDealer from gameStore:", isDealer);
        console.log("Dealer ID from players:", dealer?.id);
        console.log("Current player is dealer:", isDealer && dealer?.id === currentPlayerId);

        setDealerCheckComplete(true);
      } catch (error) {
        console.error("Error checking dealer status:", error);
        setDealerCheckComplete(true);
      }
    };

    checkDealerStatus();
  }, [isDealer, players]);

  // Only the dealer should see this button
  if (!isDealer || !dealerCheckComplete) {
    return null;
  }

  const handleDeal = async () => {
    try {
      setIsDealing(true);
      setErrorMessage(null);
      console.log("Dealing first 4 cards...");

      // Send deal first 4 event to server
      await socketService.sendGameAction("deal_first_four", {});

      // The server will handle the dealing animation and send events
      // We'll keep the button disabled until the dealing is complete
      // The onDealComplete callback will be triggered by the cards_dealt event in App.tsx
    } catch (error) {
      console.error("Error dealing first 4 cards:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to deal cards");
      setIsDealing(false);
    }
  };

  // Function to check dealer status
  const checkDealerStatus = () => {
    const currentPlayerId = socketService.getSocketId();
    const dealer = players.find(p => p.isDealer);

    console.log("Dealer check:");
    console.log("Current player ID:", currentPlayerId);
    console.log("Dealer from players array:", dealer);
    console.log("isDealer from gameStore:", isDealer);
    console.log("Dealer ID from players:", dealer?.id);
    console.log("Current player is dealer:", isDealer && dealer?.id === currentPlayerId);

    // Show an alert with the dealer status
    alert(`Dealer Status:\nCurrent player ID: ${currentPlayerId}\nDealer ID: ${dealer?.id}\nisDealer flag: ${isDealer}`);
  };

  // Function to fix dealer status by sending a request to the server
  const fixDealerStatus = async () => {
    try {
      const currentPlayerId = socketService.getSocketId();
      console.log("Attempting to fix dealer status...");
      console.log("Setting dealer to current player:", currentPlayerId);

      // Send a request to the server to set the dealer
      await socketService.sendGameAction("set_dealer", {
        dealerId: currentPlayerId
      });

      alert("Dealer status fix request sent to server. Check console for results.");
    } catch (error) {
      console.error("Error fixing dealer status:", error);
      alert("Error fixing dealer status: " + (error instanceof Error ? error.message : String(error)));
    }
  };

  return (
    <div className="fixed bottom-24 left-0 right-0 flex flex-col justify-center items-center z-50">
      {errorMessage && (
        <div className="bg-red-600 text-white px-4 py-2 rounded-md mb-4 text-center max-w-md">
          {errorMessage}
        </div>
      )}
      <div className="flex flex-col gap-4">
        <Button
          onClick={handleDeal}
          disabled={isDealing}
          className="bg-[#E1C760] text-black px-10 py-4 rounded-md text-2xl font-bold shadow-lg border-4 border-black hover:bg-[#E1C760]/90 transition-colors"
        >
          {isDealing ? "DEALING..." : "DEAL FIRST 4"}
        </Button>

        {/* <div className="flex gap-2">
          <Button
            onClick={checkDealerStatus}
            className="bg-gray-700 text-white px-6 py-2 rounded-md text-sm font-bold shadow-lg hover:bg-gray-600 transition-colors"
          >
            Debug Dealer Status
          </Button>

          <Button
            onClick={fixDealerStatus}
            className="bg-red-700 text-white px-6 py-2 rounded-md text-sm font-bold shadow-lg hover:bg-red-600 transition-colors"
          >
            Fix Dealer Status
          </Button>
        </div> */}
      </div>
    </div>
  );
}
