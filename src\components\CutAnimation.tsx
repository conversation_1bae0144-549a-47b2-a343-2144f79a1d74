"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface CutAnimationProps {
  isVisible: boolean;
  position: string | null;
  onComplete: () => void;
}

export default function CutAnimation({ isVisible, position, onComplete }: CutAnimationProps) {
  const [animationComplete, setAnimationComplete] = useState(false);

  // Reset animation state when visibility changes
  useEffect(() => {
    if (isVisible) {
      setAnimationComplete(false);
    }
  }, [isVisible]);

  // Trigger onComplete callback when animation finishes
  useEffect(() => {
    if (isVisible && animationComplete) {
      const timer = setTimeout(() => {
        onComplete();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isVisible, animationComplete, onComplete]);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="relative w-full h-56 my-4">
        <div className="absolute inset-0 flex items-center justify-center">
          {/* Original deck */}
          <motion.div
            initial={{ opacity: 1 }}
            animate={{ 
              opacity: animationComplete ? 0 : 1,
              y: animationComplete ? 20 : 0
            }}
            transition={{ duration: 0.5 }}
            className="absolute w-20 h-28 rounded-md shadow-lg"
            style={{
              backgroundImage: "url('/assets/CardBack/GR2.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              zIndex: 1
            }}
          />
          
          {/* Top half of deck */}
          <motion.div
            initial={{ x: 0, y: 0, opacity: 0 }}
            animate={{ 
              x: position === "top" ? -60 : (position === "middle" ? -50 : -40),
              y: 0,
              opacity: 1,
              zIndex: 2
            }}
            transition={{ duration: 0.8 }}
            className="absolute w-20 h-28 rounded-md shadow-lg"
            style={{
              backgroundImage: "url('/assets/CardBack/GR2.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              clipPath: position === "top" 
                ? "polygon(0 0, 100% 0, 100% 30%, 0 30%)" 
                : (position === "middle" 
                  ? "polygon(0 0, 100% 0, 100% 50%, 0 50%)" 
                  : "polygon(0 0, 100% 0, 100% 70%, 0 70%)")
            }}
          />
          
          {/* Bottom half of deck */}
          <motion.div
            initial={{ x: 0, y: 0, opacity: 0 }}
            animate={{ 
              x: position === "top" ? 60 : (position === "middle" ? 50 : 40),
              y: 0,
              opacity: 1,
              zIndex: 3
            }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="absolute w-20 h-28 rounded-md shadow-lg"
            style={{
              backgroundImage: "url('/assets/CardBack/GR2.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              clipPath: position === "top" 
                ? "polygon(0 30%, 100% 30%, 100% 100%, 0 100%)" 
                : (position === "middle" 
                  ? "polygon(0 50%, 100% 50%, 100% 100%, 0 100%)" 
                  : "polygon(0 70%, 100% 70%, 100% 100%, 0 100%)")
            }}
          />
          
          {/* New deck after cut */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ 
              opacity: animationComplete ? 1 : 0,
              y: animationComplete ? 0 : 50
            }}
            transition={{ duration: 0.5, delay: 1.2 }}
            onAnimationComplete={() => {
              setTimeout(() => setAnimationComplete(true), 500);
            }}
            className="absolute w-20 h-28 rounded-md shadow-lg"
            style={{
              backgroundImage: "url('/assets/CardBack/GR2.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              zIndex: 4
            }}
          />
        </div>
      </div>
      
      <div className="absolute bottom-10 left-0 right-0 text-center">
        <span className="text-[1.5rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
          {position === "top" ? "Cut Near Top" : 
           position === "middle" ? "Cut in Middle" : 
           "Cut Near Bottom"}
        </span>
      </div>
    </div>
  );
}
