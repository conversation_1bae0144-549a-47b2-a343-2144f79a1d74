"use client";
import { useState, useEffect } from "react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { Button } from "./ui/button";
import { motion, AnimatePresence } from "framer-motion";

interface CutOptionsProps {
  isVisible: boolean;
  onCutComplete: () => void;
}

export default function CutOptions({ isVisible, onCutComplete }: CutOptionsProps) {
  const [isCutting, setIsCutting] = useState(false);
  const [cutPosition, setCutPosition] = useState<string | null>(null);
  const [showAnimation, setShowAnimation] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  // Add useEffect for debugging
  useEffect(() => {
    console.log("CutOptions rendered with isVisible:", isVisible);
    const currentPlayerId = socketService.getSocketId();
    console.log("Current player ID in CutOptions:", currentPlayerId);
  }, [isVisible]);

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      setIsCutting(false);
      setCutPosition(null);
      setShowAnimation(false);
      setAnimationComplete(false);
    }
  }, [isVisible]);

  if (!isVisible) {
    console.log("CutOptions not visible, returning null");
    return null;
  }

  console.log("CutOptions is visible, rendering component");

  const handleCut = async (position: string | null) => {
    try {
      setIsCutting(true);
      setCutPosition(position);
      const shouldCut = position !== null;
      console.log(`Player chose to ${shouldCut ? `cut at ${position}` : 'not cut'} the deck`);

      if (shouldCut) {
        // Show animation first
        setShowAnimation(true);

        // Wait for animation to complete before sending to server
        setTimeout(async () => {
          setAnimationComplete(true);

          // Send cut decision to server
          await socketService.sendGameAction("cut_deck", {
            cut: shouldCut,
            position: position
          });

          // Wait a moment after animation completes
          setTimeout(() => {
            setIsCutting(false);
            onCutComplete();
          }, 500);
        }, 1500); // Animation duration
      } else {
        // No cut, just send to server without animation
        await socketService.sendGameAction("cut_deck", {
          cut: shouldCut,
          position: position
        });

        // Brief delay for UI feedback
        setTimeout(() => {
          setIsCutting(false);
          onCutComplete();
        }, 500);
      }
    } catch (error) {
      console.error("Error cutting deck:", error);
      setIsCutting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
      <div className="bg-black border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full">
        <div className="border-b border-[#a07a4a] bg-black px-8 py-2 text-center rounded-t-md">
          <span className="text-[1.5rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
            Cut
          </span>
        </div>

        {/* Card Cutting Animation */}
        <AnimatePresence>
          {showAnimation && (
            <div className="relative w-full h-56 my-4">
              <div className="absolute inset-0 flex items-center justify-center">
                {/* Original deck */}
                <motion.div
                  initial={{ opacity: 1 }}
                  animate={{
                    opacity: animationComplete ? 0 : 1,
                    y: animationComplete ? 20 : 0
                  }}
                  transition={{ duration: 0.5 }}
                  className="absolute w-20 h-28 rounded-md shadow-lg"
                  style={{
                    backgroundImage: "url('/assets/CardBack/GR2.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    zIndex: 1
                  }}
                />

                {/* Top half of deck */}
                <motion.div
                  initial={{ x: 0, y: 0, opacity: 0 }}
                  animate={{
                    x: cutPosition === "top" ? -60 : (cutPosition === "middle" ? -50 : -40),
                    y: 0,
                    opacity: showAnimation ? 1 : 0,
                    zIndex: 2
                  }}
                  transition={{ duration: 0.8 }}
                  className="absolute w-20 h-28 rounded-md shadow-lg"
                  style={{
                    backgroundImage: "url('/assets/CardBack/GR2.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    clipPath: cutPosition === "top"
                      ? "polygon(0 0, 100% 0, 100% 30%, 0 30%)"
                      : (cutPosition === "middle"
                        ? "polygon(0 0, 100% 0, 100% 50%, 0 50%)"
                        : "polygon(0 0, 100% 0, 100% 70%, 0 70%)")
                  }}
                />

                {/* Bottom half of deck */}
                <motion.div
                  initial={{ x: 0, y: 0, opacity: 0 }}
                  animate={{
                    x: cutPosition === "top" ? 60 : (cutPosition === "middle" ? 50 : 40),
                    y: 0,
                    opacity: showAnimation ? 1 : 0,
                    zIndex: 3
                  }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="absolute w-20 h-28 rounded-md shadow-lg"
                  style={{
                    backgroundImage: "url('/assets/CardBack/GR2.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    clipPath: cutPosition === "top"
                      ? "polygon(0 30%, 100% 30%, 100% 100%, 0 100%)"
                      : (cutPosition === "middle"
                        ? "polygon(0 50%, 100% 50%, 100% 100%, 0 100%)"
                        : "polygon(0 70%, 100% 70%, 100% 100%, 0 100%)")
                  }}
                />

                {/* New deck after cut */}
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  animate={{
                    opacity: animationComplete ? 1 : 0,
                    y: animationComplete ? 0 : 50
                  }}
                  transition={{ duration: 0.5, delay: 1.2 }}
                  className="absolute w-20 h-28 rounded-md shadow-lg"
                  style={{
                    backgroundImage: "url('/assets/CardBack/GR2.svg')",
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    zIndex: 4
                  }}
                />
              </div>
            </div>
          )}
        </AnimatePresence>

        {!showAnimation && (
          <>
            <div className="flex gap-1 py-5 px-2 bg-blackglass rounded-b-md justify-center">
              <button
                onClick={() => handleCut('top')}
                disabled={isCutting}
                className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  Top
                </span>
              </button>
              <button
                onClick={() => handleCut('middle')}
                disabled={isCutting}
                className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  Middle
                </span>
              </button>
              <button
                onClick={() => handleCut('bottom')}
                disabled={isCutting}
                className="w-[6rem] rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  Bottom
                </span>
              </button>
            </div>

            <div className="mt-4 flex justify-center">
              <button
                onClick={() => handleCut(null)}
                disabled={isCutting}
                className="w-full rounded border border-[#a07a4a] bg-transparent px-3 py-1.5 text-sm font-medium text-yellow-400 transition-colors hover:bg-[#a07a4a]/20"
              >
                <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
                  No Cut
                </span>
              </button>
            </div>
          </>
        )}

        {/* Status Message */}
        {isCutting && !showAnimation && (
          <div className="mt-4 text-center">
            <span className="text-[1rem] font-bold bg-gold1 text-transparent bg-clip-text leading-tight">
              Processing...
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
