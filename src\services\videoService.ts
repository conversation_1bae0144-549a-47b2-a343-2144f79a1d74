import { io, Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';

// Define the video server URL
const getVideoServerUrl = () => {
  const savedPort = localStorage.getItem('thunee_video_port');
  return `http://localhost:${savedPort || '3002'}`;
};

let VIDEO_SERVER_URL = getVideoServerUrl();

class VideoService {
  private socket: Socket | null = null;
  private listeners: Map<string, Array<(data: any) => void>> = new Map();
  private roomId: string | null = null;
  private userName: string = 'Unknown Player';

  // Connect to the video WebSocket server
  connect(userName: string): Promise<void> {
    this.userName = userName;

    return new Promise((resolve, reject) => {
      try {
        // If already connected with the same user name, just resolve
        if (this.socket && this.socket.connected) {
          console.log('Video service: Already connected');
          this.socket.emit('register', { name: userName });
          resolve();
          return;
        }

        // Close existing connection if any
        if (this.socket) {
          this.socket.close();
        }

        // Create new connection
        this.socket = io(VIDEO_SERVER_URL, {
          transports: ['websocket', 'polling'],
          reconnectionAttempts: 10,
          reconnectionDelay: 1000,
          timeout: 20000,
          autoConnect: true,
          reconnection: true
        });

        this.socket.on('connect', () => {
          console.log('Video service: Connected to WebSocket server');
          this.socket?.emit('register', { name: userName });

          // Set up error handlers
          this.setupErrorHandlers();

          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('Video service: Connection error:', error);
          reject(error);
        });

        // Add a timeout to reject if connection takes too long
        setTimeout(() => {
          if (this.socket && !this.socket.connected) {
            reject(new Error('Video service: Connection timeout'));
          }
        }, 20000);
      } catch (error) {
        console.error('Video service: Error initializing socket:', error);
        reject(error);
      }
    });
  }

  // Set up error handlers
  private setupErrorHandlers(): void {
    if (!this.socket) return;

    // Handle signal errors
    this.socket.on('signal_error', ({ to, error }) => {
      console.error(`Video service: Signal error to ${to}: ${error}`);

      // Notify listeners
      const callbacks = this.listeners.get('signal_error');
      if (callbacks) {
        callbacks.forEach(callback => callback({ to, error }));
      }
    });

    // Handle disconnect
    this.socket.on('disconnect', (reason) => {
      console.warn(`Video service: Disconnected: ${reason}`);

      // Notify listeners
      const callbacks = this.listeners.get('disconnect');
      if (callbacks) {
        callbacks.forEach(callback => callback({ reason }));
      }
    });
  }

  // Disconnect from the WebSocket server
  disconnect(): void {
    if (this.socket) {
      if (this.roomId) {
        this.leaveRoom(this.roomId);
      }
      this.socket.disconnect();
      this.socket = null;
      console.log('Video service: Disconnected from WebSocket server');
    }
  }

  // Join a video room
  joinRoom(roomId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Video service: Socket not connected'));
        return;
      }

      this.roomId = roomId;
      this.socket.emit('join_room', { roomId, name: this.userName });
      console.log(`Video service: Joining room ${roomId} as ${this.userName}`);
      resolve();
    });
  }

  // Leave a video room
  leaveRoom(roomId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Video service: Socket not connected'));
        return;
      }

      this.socket.emit('leave_room', { roomId });
      this.roomId = null;
      console.log(`Video service: Leaving room ${roomId}`);
      resolve();
    });
  }

  // Send a signal to another peer
  sendSignal(to: string, signal: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Video service: Socket not connected'));
        return;
      }

      if (!this.roomId) {
        reject(new Error('Video service: Not in a room'));
        return;
      }

      this.socket.emit('signal', { roomId: this.roomId, to, signal });
      resolve();
    });
  }

  // Get socket ID
  getSocketId(): string | null {
    return this.socket ? this.socket.id : null;
  }

  // Add an event listener
  on(event: string, callback: (data: any) => void): void {
    if (!this.socket) {
      console.error('Video service: Socket not connected');
      return;
    }

    // Store the callback in our listeners map
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);

    // Add the listener to the socket
    this.socket.on(event, callback);
  }

  // Remove an event listener
  off(event: string, callback?: (data: any) => void): void {
    if (!this.socket) {
      return;
    }

    if (callback) {
      // Remove specific callback
      this.socket.off(event, callback);

      // Update our listeners map
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    } else {
      // Remove all callbacks for this event
      this.socket.off(event);
      this.listeners.delete(event);
    }
  }

  // Update the server URL
  updateServerUrl(port: string): void {
    VIDEO_SERVER_URL = `http://localhost:${port}`;
    localStorage.setItem('thunee_video_port', port);
    console.log(`Video service: Server URL updated to: ${VIDEO_SERVER_URL}`);
  }
}

// Create a singleton instance
const videoService = new VideoService();

export default videoService;
