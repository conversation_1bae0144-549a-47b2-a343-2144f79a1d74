"use client";
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from "framer-motion";

// Define card types and positions
type CardState = {
  id: number;
  dealt: boolean;
  x: number;
  y: number;
  rotation: number;
  index: number;
};

interface PlayerCardAnimationProps {
  isVisible: boolean;
  onComplete: () => void;
  cardCount: 2 | 4; // Either 2 or 4 cards
}

export default function PlayerCardAnimation({ isVisible, onComplete, cardCount }: PlayerCardAnimationProps) {
  const [isDealing, setIsDealing] = useState(false);
  const [cards, setCards] = useState<CardState[]>([]);

  // Reset cards when component becomes visible or card count changes
  useEffect(() => {
    if (isVisible) {
      resetCards();
      // Start dealing animation after a short delay
      setTimeout(() => {
        dealCards();
      }, 500);
    }
  }, [isVisible, cardCount]);

  // Initialize cards in their starting position
  const resetCards = () => {
    const newCards: CardState[] = [];

    // Create cards for player's hand
    for (let i = 0; i < cardCount; i++) {
      newCards.push({
        id: i,
        dealt: false,
        x: 0,
        y: 0,
        rotation: 0,
        index: i // Store the index in the card state
      });
    }

    setCards(newCards);
    setIsDealing(false);
  };

  // Handle the animation of dealing cards
  const dealCards = () => {
    if (isDealing) return;
    setIsDealing(true);

    // Deal each card with a delay
    const dealCard = (index: number) => {
      if (index >= cards.length) {
        // All cards dealt, wait a moment then call onComplete
        setTimeout(() => {
          setIsDealing(false);
          onComplete();
        }, 1000);
        return;
      }

      setTimeout(() => {
        setCards(prevCards => {
          const newCards = [...prevCards];
          const card = newCards[index];

          if (!card) {
            console.error(`Card at index ${index} is undefined`);
            return prevCards;
          }

          // Calculate the position for player's hand (fan out the cards)
          const fanWidth = cardCount <= 2 ? 100 : 180;
          const offset = fanWidth / (cardCount - 1 || 1);
          const cardIndex = card.index !== undefined ? card.index : index;
          const x = (-fanWidth / 2) + (offset * cardIndex);
          const y = 120; // Position at bottom of the table
          const rotation = -10 + (20 / (cardCount - 1 || 1)) * cardIndex; // Slight fan rotation

          newCards[index] = {
            ...card,
            dealt: true,
            x,
            y,
            rotation
          };

          return newCards;
        });

        dealCard(index + 1);
      }, 200);
    };

    dealCard(0);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black flex items-center justify-center z-50">
      {/* Full screen background with table texture */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-900 to-green-950"></div>

      {/* Game table overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-full h-full max-w-4xl max-h-4xl relative">
          {/* Table border */}
          <div className="absolute inset-0 border-8 border-[#E1C760] rounded-3xl opacity-30"></div>

          {/* Dealer position with glow effect */}
          <div className="absolute top-1/2 left-1/2 w-20 h-20 rounded-full bg-[#E1C760] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center shadow-lg shadow-yellow-500/50 border-2 border-yellow-300">
            <span className="text-black font-bold text-lg">DEALER</span>
          </div>

          {/* Player position with glow effect */}
          <div className="absolute bottom-[15%] left-1/2 w-24 h-24 rounded-full bg-blue-600 transform -translate-x-1/2 flex items-center justify-center shadow-lg shadow-blue-500/50 border-4 border-blue-300">
            <span className="text-white font-bold text-xl">YOU</span>
          </div>

          {/* Dealing path visualization - enhanced curved line */}
          <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M 500,500 C 500,600 500,700 500,850"
              stroke="rgba(225, 199, 96, 0.4)"
              strokeWidth="4"
              strokeDasharray="10,10"
              fill="none"
            />
          </svg>

          {/* Deck in center */}
          <div className="absolute top-1/2 left-1/2 w-24 h-36 rounded-md shadow-lg border-2 border-[#E1C760] transform -translate-x-1/2 -translate-y-1/2" style={{
            backgroundImage: "url('/assets/CardBack/GR2.svg')",
            backgroundSize: "cover",
            backgroundPosition: "center",
            zIndex: 5
          }}></div>

          {/* Cards */}
          <AnimatePresence>
            {cards.map(card => (
              <motion.div
                key={card.id}
                className="absolute top-1/2 left-1/2 w-24 h-36 rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/assets/CardBack/GR2.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  zIndex: card.id
                }}
                initial={{
                  x: 0,
                  y: -50,
                  opacity: 0,
                  rotate: 0,
                  scale: 0.8
                }}
                animate={{
                  x: card.dealt ? card.x * 1.5 : 0, // Increased distance for better visibility
                  y: card.dealt ? card.y * 1.5 : 0, // Increased distance for better visibility
                  opacity: 1,
                  rotate: card.dealt ? card.rotation : 0,
                  scale: 1
                }}
                exit={{ opacity: 0 }}
                transition={{
                  duration: 0.5,
                  ease: "easeOut",
                  delay: card.index ? card.index * 0.1 : 0 // Stagger the animations
                }}
                whileHover={{
                  y: card.dealt ? card.y * 1.5 - 15 : -50,
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
              />
            ))}
          </AnimatePresence>
        </div>
      </div>

      {/* Status message */}
      <div className="absolute bottom-20 left-0 right-0 text-center">
        <div className="bg-black/70 mx-auto max-w-md py-4 px-8 rounded-full border-2 border-[#E1C760]">
          <span className="text-[2rem] font-bold text-[#E1C760] leading-tight">
            {isDealing ? `Receiving ${cardCount} cards...` : 'Ready to receive cards'}
          </span>
        </div>
      </div>
    </div>
  );
}
