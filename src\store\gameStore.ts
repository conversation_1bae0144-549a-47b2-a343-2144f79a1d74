import { create } from 'zustand';
import { dispatchCardDealtEvent, dispatchCardsDealingCompleteEvent } from '../utils/eventDispatcher';
import socketService from '../services/socketService';
import { toast } from 'sonner';
import { PlayerPosition } from '../utils/playerPositionUtils';
import { playSound, SOUNDS } from '../utils/soundUtils';

// Create a module-level variable to track cards globally
const _globalCardTracker = new Set<string>();

export type Card = {
  id: string;
  value: string;
  suit: string;
  image: string;
  points: number;
};

export type Player = {
  id: string;
  name: string;
  avatar: string;
  isDealer?: boolean;
  isTrumpSelector?: boolean;
  team: 1 | 2;
  isCurrentTurn?: boolean;
  position?: PlayerPosition; // Fixed position (1-4) in the game
};

export type Round = {
  id: number;
  cards: Card[];
  winner?: Player;
};

export type Hand = {
  id: number;
  cards: Card[];
  leadSuit: string | null;
  winner: Player | null;
  points: number;
  winReason?: string;
  winningTeam?: 1 | 2;
};

export type Ball = {
  id: number;
  hands: Hand[];
  winner: 1 | 2 | null; // Team 1 or Team 2
  points: {
    team1: number;
    team2: number;
  };
};

export type GameState = {
  players: Player[];
  hand: Card[];
  initialHand: Card[]; // Store the first 4 cards separately
  playedCards: Card[];
  rounds: Round[];
  currentRound: number;
  currentHand: number; // Current hand number (1-6)
  currentBall: number; // Current ball number
  hands: Hand[];
  balls: Ball[];
  trumpSuit: string | null;
  isLastCard: boolean;
  isDealer: boolean;
  isTrumpSelector: boolean;
  isCurrentTurn: boolean;
  currentTurn: string | null; // Player ID
  gameStarted: boolean;
  gameEnded: boolean;
  winner: 1 | 2 | null; // Team 1 or Team 2
  scores: {
    team1: number;
    team2: number;
  };
  // Ball scores (number of balls won by each team)
  ballScores: {
    team1: number;
    team2: number;
  };
  teamNames: {
    1: string;
    2: string;
  };
  // Play timeframe (in seconds) - how long a player has to play a card
  playTimeframe: number;
  // Remaining time for current player's turn (in seconds)
  turnTimeRemaining: number | null;
  // Bidding related state
  currentBid: number;
  highestBidder: string | null;
  canBid: boolean;
  biddingComplete: boolean;
  // Target scores for each team (normally 105, but can be reduced based on bid)
  targetScores: {
    team1: number;
    team2: number;
  };
  // Which team is the bidding team (the team that won the bid)
  biddingTeam: 1 | 2 | null;
  // Thunee related state
  thuneePlayerId: string | null; // Player ID of the player who called Thunee
  thuneePlayerName: string | null; // Name of the player who called Thunee
  thuneePlayerTeam: number | null; // Team of the player who called Thunee
  isRoyalThunee: boolean; // Whether this is a Royal Thunee (reversed card rankings)
  // Jordhi related state
  jordhiCalls: {
    playerId: string;
    playerName: string;
    playerTeam: 1 | 2;
    value: number;
    handNumber: number;
    isValid?: boolean; // Legacy field
    isValidCards?: boolean;
    isValidHandCount?: boolean;
    isFullyValid?: boolean;
    jordhiSuit?: string | null;
    jordhiCards?: Array<{ suit: string, value: string }>;
    cardsRevealed?: boolean; // Whether the cards were revealed to all players
  }[];
  jordhiHistoryOpen: boolean; // Whether the Jordhi history panel is open
  incorrectJordhiModalOpen: boolean; // Whether the incorrect Jordhi modal is open
  neverFollowSuitModalOpen: boolean; // Whether the Never Follow Suit modal is open
  underChoppedModalOpen: boolean; // Whether the Under Chopped modal is open
  underChoppedResult: any; // Result of the Under Chopped 4-ball claim
  underChoppedResultOpen: boolean; // Whether the Under Chopped result display is open
  currentPlayerId: string | null; // Current player's ID
  error: string | null;
};

export type GameActions = {
  startGame: () => Promise<void>;
  dealCards: () => Promise<void>;
  selectTrump: (suit: string) => Promise<void>;
  playCard: (card: Card) => Promise<void>;
  placeBid: (bid: number) => Promise<void>;
  passBid: () => Promise<void>;
  voteTimeframe: (timeframe: number) => Promise<void>;
  resetGame: () => void;
  setError: (error: string | null) => void;
  updateGameState: (state: Partial<GameState>) => void;
};

// Initial state
const initialState: GameState = {
  players: [],
  hand: [],
  initialHand: [],
  playedCards: [],
  rounds: [],
  currentRound: 0,
  currentHand: 1,
  currentBall: 1,
  hands: [],
  balls: [],
  trumpSuit: null,
  isLastCard: false,
  isDealer: false,
  isTrumpSelector: false,
  isCurrentTurn: false,
  currentTurn: null,
  gameStarted: false,
  gameEnded: false,
  winner: null,
  scores: {
    team1: 0,
    team2: 0,
  },
  ballScores: {
    team1: 0,
    team2: 0,
  },
  teamNames: {
    1: 'Team 1',
    2: 'Team 2',
  },
  // Play timeframe (in seconds) - default is 3 seconds
  playTimeframe: 3,
  // Remaining time for current player's turn (in seconds)
  turnTimeRemaining: null,
  // Bidding related state
  currentBid: 0,
  highestBidder: null,
  canBid: false,
  biddingComplete: false,
  // Target scores for each team (normally 105, but can be reduced based on bid)
  targetScores: {
    team1: 105, // Default target score
    team2: 105, // Default target score
  },
  // Which team is the bidding team (the team that won the bid)
  biddingTeam: null,
  // Thunee related state
  thuneePlayerId: null,
  thuneePlayerName: null,
  thuneePlayerTeam: null,
  isRoyalThunee: false,
  // Jordhi related state
  jordhiCalls: [],
  jordhiHistoryOpen: false,
  incorrectJordhiModalOpen: false,
  neverFollowSuitModalOpen: false,
  underChoppedModalOpen: false,
  underChoppedResult: null,
  underChoppedResultOpen: false,
  currentPlayerId: null,
  error: null,
};

// Create the store
export const useGameStore = create<GameState & GameActions>((set, get) => ({
  ...initialState,

  startGame: async () => {
    try {
      await socketService.sendGameAction('start_game', {});
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to start game' });
      return Promise.reject(error);
    }
  },

  dealCards: async () => {
    try {
      await socketService.sendGameAction('deal_cards', {});
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to deal cards' });
      return Promise.reject(error);
    }
  },

  selectTrump: async (suit: string) => {
    try {
      await socketService.sendGameAction('select_trump', { suit });
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to select trump' });
      return Promise.reject(error);
    }
  },

  playCard: async (card: Card) => {
    try {
      console.log('playCard function called with card:', card);

      // Get current state
      const { hand, playedCards, players, currentTurn, isCurrentTurn } = get();
      console.log('Current state before playing card:', {
        currentTurn,
        isCurrentTurn,
        handSize: hand.length,
        playedCardsSize: playedCards.length
      });

      // Find the current player
      const currentPlayer = players.find(p => p.id === currentTurn);

      if (!currentPlayer) {
        console.error('Cannot determine current player');
        throw new Error('Cannot determine current player');
      }

      console.log(`Current player: ${currentPlayer.name} (${currentPlayer.id}), Position: ${currentPlayer.position}`);

      // Check if the card already has a playedBy property, otherwise add it
      const cardWithPlayer = {
        ...card,
        playedBy: (card as any).playedBy || currentPlayer.id,
        playerPosition: currentPlayer.position // Add the player's position to the card
      };

      console.log('Card with player ID and position:', cardWithPlayer);

      // Check if this player already has a card in the played cards
      const playerAlreadyPlayed = playedCards.some(c => (c as any).playedBy === currentPlayer.id);
      if (playerAlreadyPlayed) {
        console.warn(`Player ${currentPlayer.id} already has a card in the played cards. This might be a duplicate play.`);
        // We'll still allow it and let the server handle it
      }

      // Update local state
      set({
        hand: hand.filter(c => c.id !== card.id),
        playedCards: [...playedCards, cardWithPlayer],
        isCurrentTurn: false // Immediately set turn to false to prevent double plays
      });

      console.log('Local state updated, sending card to server...');

      // Send to server - make sure to send the card with the player ID
      await socketService.sendGameAction('play_card', { card: cardWithPlayer });
      console.log('Card successfully sent to server');

      // Log all played cards for debugging
      const updatedPlayedCards = [...playedCards, cardWithPlayer];
      console.log('All played cards after local update:');
      updatedPlayedCards.forEach((c, index) => {
        console.log(`Card ${index + 1}: ${c.value} of ${c.suit} played by ${(c as any).playedBy}`);
      });

      return Promise.resolve();
    } catch (error) {
      // Revert optimistic update on error
      console.error('Error playing card:', error);
      const { hand, playedCards } = get();
      set({
        hand: [...hand, card],
        playedCards: playedCards.filter(c => c.id !== card.id),
        isCurrentTurn: true, // Restore turn if there was an error
        error: error instanceof Error ? error.message : 'Failed to play card'
      });
      return Promise.reject(error);
    }
  },

  placeBid: async (bid: number) => {
    try {
      console.log(`Placing bid of ${bid}`);

      // Update local state optimistically
      set({
        currentBid: bid,
        highestBidder: socketService.getSocketId(),
        canBid: false // Disable bidding until it's this player's turn again
      });

      // Send to server
      await socketService.sendGameAction('place_bid', { bid });
      console.log('Bid successfully sent to server');
      return Promise.resolve();
    } catch (error) {
      // Revert optimistic update on error
      console.error('Error placing bid:', error);
      set({
        canBid: true, // Re-enable bidding
        error: error instanceof Error ? error.message : 'Failed to place bid'
      });
      return Promise.reject(error);
    }
  },

  passBid: async () => {
    try {
      console.log('Passing on bidding');

      // Update local state
      set({
        canBid: false // Disable bidding
      });

      // Send to server
      await socketService.sendGameAction('pass_bid', {});
      console.log('Pass bid successfully sent to server');
      return Promise.resolve();
    } catch (error) {
      // Revert optimistic update on error
      console.error('Error passing bid:', error);
      set({
        canBid: true, // Re-enable bidding
        error: error instanceof Error ? error.message : 'Failed to pass bid'
      });
      return Promise.reject(error);
    }
  },

  voteTimeframe: async (timeframe: number) => {
    try {
      console.log(`Voting for timeframe: ${timeframe} seconds`);

      // Send to server
      await socketService.sendGameAction('vote_timeframe', { timeframe });
      console.log('Timeframe vote successfully sent to server');
      return Promise.resolve();
    } catch (error) {
      console.error('Error voting for timeframe:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to vote for timeframe'
      });
      return Promise.reject(error);
    }
  },

  resetGame: () => {
    set(initialState);
  },

  setError: (error) => set({ error }),

  updateGameState: (state) => set(state),
}));

// Set up socket event listeners for game events
export const setupGameListeners = () => {
  const { updateGameState, setError } = useGameStore.getState();

  // Clean up any existing listeners to prevent duplicates
  socketService.off('game_started');
  socketService.off('receive_cards');
  socketService.off('card_dealt');
  socketService.off('cards_dealt');
  socketService.off('trump_selected');
  socketService.off('player_turn');
  socketService.off('card_played');
  socketService.off('round_completed');
  socketService.off('game_ended');
  socketService.off('game_error');
  socketService.off('dealer_found');
  socketService.off('dealer_updated');
  socketService.off('bid_placed');
  socketService.off('bid_passed');
  socketService.off('your_turn_to_bid');
  socketService.off('bidding_complete');
  socketService.off('your_turn_to_select_trump');
  socketService.off('jordhi_called');
  socketService.off('jordhi_called_by_other');
  socketService.off('jordhi_error');
  socketService.off('jordhi_notification');
  socketService.off('jordhi_toast_notification');
  socketService.off('under_chopped_result');
  socketService.off('timeframe_vote_received');
  socketService.off('timeframe_vote_results');
  socketService.off('turn_timer_update');

  // Game started event
  socketService.on('game_started', (data: { players: Player[], teamNames?: { 1: string, 2: string } }) => {
    console.log('Game started with players:', data.players);

    // Find if current player is dealer or trump selector
    const currentPlayerId = socketService.getSocketId();
    const isDealer = data.players.some(p => p.id === currentPlayerId && p.isDealer);
    const isTrumpSelector = data.players.some(p => p.id === currentPlayerId && p.isTrumpSelector);

    // Log player positions if they exist
    const playersWithPositions = data.players.filter(p => p.position);
    if (playersWithPositions.length > 0) {
      console.log('Players with positions:',
        playersWithPositions.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`)
      );
    } else {
      console.log('No players have positions assigned yet');
    }

    updateGameState({
      gameStarted: true,
      players: data.players,
      isDealer,
      isTrumpSelector,
      currentPlayerId: currentPlayerId,
      teamNames: data.teamNames || { 1: 'Team 1', 2: 'Team 2' }
    });
  });

  // Helper functions for card handling
  const hasDuplicates = (cards: Card[]): boolean => {
    const cardKeys = new Set<string>();
    for (const card of cards) {
      const key = `${card.value}_${card.suit}`;
      if (cardKeys.has(key)) {
        console.error(`Duplicate card found: ${card.value} of ${card.suit}`);
        return true;
      }
      cardKeys.add(key);
    }
    return false;
  };

  const removeDuplicates = (cards: Card[]): Card[] => {
    const uniqueCards: Card[] = [];
    const cardKeys = new Set<string>();

    for (const card of cards) {
      const key = `${card.value}_${card.suit}`;
      if (!cardKeys.has(key)) {
        cardKeys.add(key);
        uniqueCards.push(card);
      } else {
        console.warn(`Skipping duplicate card: ${card.value} of ${card.suit}`);
      }
    }

    return uniqueCards;
  };



  // Function to check if a card has been seen globally
  const isCardSeenGlobally = (card: Card): boolean => {
    const key = `${card.value}_${card.suit}`;
    return _globalCardTracker.has(key);
  };

  // Function to mark a card as seen globally
  const markCardSeenGlobally = (card: Card): void => {
    const key = `${card.value}_${card.suit}`;
    _globalCardTracker.add(key);
  };

  // Note: We check for global duplicates directly in the receive_cards handler

  // Track the last processed hand to prevent infinite loops
  let lastProcessedHandString = "";

  // Receive cards event
  socketService.on('receive_cards', (data: { cards: Card[] }) => {
    const { hand } = useGameStore.getState();

    // Create a string representation of the received cards for comparison
    const receivedCardsString = JSON.stringify(data.cards.map(card => `${card.value}_${card.suit}`).sort());

    // Skip processing if we've already processed these exact cards
    if (receivedCardsString === lastProcessedHandString) {
      console.log('Skipping duplicate receive_cards event with same cards');
      return;
    }

    // Update the last processed hand string
    lastProcessedHandString = receivedCardsString;

    // Check for global duplicates (cards that have been seen in other players' hands)
    console.log('Checking received cards for global duplicates...');
    data.cards.forEach(card => {
      if (isCardSeenGlobally(card)) {
        console.error(`GLOBAL DUPLICATE DETECTED: Card ${card.value} of ${card.suit} has been seen in another player's hand!`);
      } else {
        markCardSeenGlobally(card);
      }
    });

    // If this is the first time receiving cards (first 4 cards)
    if (hand.length === 0) {
      console.log('Received initial cards:', data.cards);

      // Check for duplicates in the initial cards
      if (hasDuplicates(data.cards)) {
        console.warn('Duplicates found in initial cards, removing duplicates');
        const uniqueCards = removeDuplicates(data.cards);
        console.log('Unique initial cards:', uniqueCards);
        updateGameState({
          hand: uniqueCards,
          initialHand: uniqueCards
        });
      } else {
        updateGameState({
          hand: data.cards,
          initialHand: data.cards
        });
      }
    } else {
      // If we already have cards, check if these are new cards or a complete hand
      console.log('Received additional cards:', data.cards);
      console.log('Current hand before update:', hand);
      console.log('New cards length:', data.cards.length);

      // If we received a complete hand (all 6 cards), use it directly
      if (data.cards.length === 6) {
        console.log('Received complete hand of 6 cards, using it directly');

        // Check for duplicates
        if (hasDuplicates(data.cards)) {
          console.warn('Duplicates found in complete hand, removing duplicates');
          const uniqueCards = removeDuplicates(data.cards);
          console.log('Unique complete hand:', uniqueCards);
          updateGameState({ hand: uniqueCards });
        } else {
          console.log('Using complete hand of 6 cards');
          updateGameState({ hand: data.cards });
        }
        return;
      }

      // If we already have 6 cards, check if the new cards are different
      if (hand.length === 6) {
        console.log('Already have 6 cards, checking if new cards are different');

        // Check if the new cards contain any cards we don't already have
        const newUniqueCards = data.cards.filter(newCard =>
          !hand.some(existingCard =>
            existingCard.suit === newCard.suit && existingCard.value === newCard.value
          )
        );

        if (newUniqueCards.length > 0) {
          console.log(`Found ${newUniqueCards.length} new unique cards, replacing existing hand`);

          // If we have new unique cards, use the new cards (they might be the final 2)
          // Combine with existing cards to ensure we have 6 total
          const combinedCards = [...hand.slice(0, 6 - newUniqueCards.length), ...newUniqueCards];

          // Check for duplicates
          if (hasDuplicates(combinedCards)) {
            console.warn('Duplicates found in combined cards, removing duplicates');
            const uniqueCards = removeDuplicates(combinedCards);
            console.log('Unique combined cards:', uniqueCards);
            updateGameState({ hand: uniqueCards });
          } else {
            console.log('Using combined cards:', combinedCards);
            updateGameState({ hand: combinedCards });
          }
          return;
        } else {
          console.log('No new unique cards found, keeping existing hand');
          return;
        }
      }

      // Otherwise, we're receiving just the final cards
      console.log('Received final cards, combining with existing hand');
      console.log('Existing hand:', hand);
      console.log('New cards:', data.cards);

      // Check if these are new cards or duplicates of what we already have
      const newCards = data.cards.filter(newCard =>
        !hand.some(existingCard =>
          existingCard.suit === newCard.suit && existingCard.value === newCard.value
        )
      );

      console.log('Filtered new cards:', newCards);

      if (newCards.length === 0) {
        console.log('No new cards to add, keeping existing hand');
        return; // No new cards to add
      }

      // Combine existing hand with new cards
      const allCards = [...hand, ...newCards];

      // Check for duplicates in the combined cards
      if (hasDuplicates(allCards)) {
        console.warn('Duplicates found in combined cards, removing duplicates');
        const uniqueCards = removeDuplicates(allCards);
        console.log('Unique combined cards:', uniqueCards);
        updateGameState({ hand: uniqueCards });
      } else {
        console.log('Combined all cards:', allCards);
        updateGameState({ hand: allCards });
      }
    }

    // Log the total number of unique cards seen globally
    console.log(`Total unique cards seen globally: ${_globalCardTracker.size} (should be ≤ 24)`);
    if (_globalCardTracker.size > 24) {
      console.error('ERROR: More than 24 unique cards have been seen! This indicates duplicates across players.');
    }
  });

  // Card dealt event - for the dealing animation
  socketService.on('card_dealt', (data: { playerId: string, card: Card, isDealer: boolean }) => {
    console.log('Card dealt event received:', data);
    // Dispatch a DOM event so our components can react to it
    dispatchCardDealtEvent(data);
  });

  // Cards dealt event - when all cards have been dealt
  socketService.on('cards_dealt', (data: { dealerId: string }) => {
    console.log('All cards dealt event received:', data);
    // Dispatch a DOM event so our components can react to it
    dispatchCardsDealingCompleteEvent();
  });

  // Trump selected event
  socketService.on('trump_selected', (data: { suit: string, selector: string }) => {
    updateGameState({ trumpSuit: data.suit });
  });

  // Player turn event
  socketService.on('player_turn', (data: { playerId: string, isFirstPlayerAfterThunee?: boolean }) => {
    const currentPlayerId = socketService.getSocketId();
    const isCurrentPlayerTurn = data.playerId === currentPlayerId;
    const { playTimeframe } = useGameStore.getState();

    console.log(`PLAYER TURN EVENT RECEIVED: Player ${data.playerId} (is current player's turn: ${isCurrentPlayerTurn})`);
    console.log(`Is first player after Thunee: ${data.isFirstPlayerAfterThunee}`);
    console.log(`Current socket ID: ${currentPlayerId}`);

    updateGameState({
      currentTurn: data.playerId,
      isCurrentTurn: isCurrentPlayerTurn,
      turnTimeRemaining: playTimeframe // Reset turn timer to the full timeframe
    });

    // Log the state after update
    setTimeout(() => {
      const state = useGameStore.getState();
      console.log('Game state after player_turn update:', {
        currentTurn: state.currentTurn,
        isCurrentTurn: state.isCurrentTurn,
        playedCards: state.playedCards.length,
        turnTimeRemaining: state.turnTimeRemaining
      });
    }, 100);
  });

  // Timeframe vote received event
  socketService.on('timeframe_vote_received', (data: {
    playerId: string;
    playerName: string;
  }) => {
    console.log(`Player ${data.playerName} (${data.playerId}) has voted for a timeframe`);
  });

  // Timeframe vote results event
  socketService.on('timeframe_vote_results', (data: {
    results: Record<number, number>;
    selectedTimeframe: number;
  }) => {
    console.log(`Timeframe voting results:`, data.results);
    console.log(`Selected timeframe: ${data.selectedTimeframe} seconds`);

    // Update the game state with the selected timeframe
    updateGameState({
      playTimeframe: data.selectedTimeframe
    });
  });

  // Turn timer update event
  socketService.on('turn_timer_update', (data: {
    playerId: string;
    timeRemaining: number;
  }) => {
    const currentPlayerId = socketService.getSocketId();
    const isCurrentPlayerTurn = data.playerId === currentPlayerId;

    // Only update the timer if it's for the current player's turn
    if (isCurrentPlayerTurn) {
      updateGameState({
        turnTimeRemaining: data.timeRemaining
      });
    }
  });

  // Card played event
  socketService.on('card_played', (data: {
    card: Card,
    playerId: string,
    playerInfo?: { name: string, team: 1 | 2, position?: PlayerPosition },
    allPlayers?: Array<{ id: string, name: string, team: 1 | 2, position?: PlayerPosition }>
  }) => {
    const { playedCards, currentTurn, players } = useGameStore.getState();

    // Make sure the card has the playerId attached
    const cardWithPlayer = {
      ...data.card,
      playedBy: data.playerId, // Ensure playedBy is set to the player who played the card
      playerPosition: data.playerInfo?.position, // Store the player's position
      allPlayers: data.allPlayers // Store the player list from the server
    };

    console.log('Card played event received:', {
      card: cardWithPlayer,
      playerId: data.playerId,
      playerInfo: data.playerInfo,
      playerPosition: data.playerInfo?.position,
      allPlayersCount: data.allPlayers?.length || 0
    });

    // If we received player info from the server, update our local players array
    // This ensures we have the latest player information for all players
    let updatedPlayers = [...players];
    if (data.allPlayers && data.allPlayers.length > 0) {
      // For each player in the allPlayers array, update our local players array
      data.allPlayers.forEach(serverPlayer => {
        const localPlayerIndex = updatedPlayers.findIndex(p => p.id === serverPlayer.id);
        if (localPlayerIndex === -1) {
          // If the player doesn't exist in our local array, add them
          updatedPlayers.push({
            id: serverPlayer.id,
            name: serverPlayer.name,
            team: serverPlayer.team,
            position: serverPlayer.position, // Include position if available
            avatar: '' // Default avatar
          });
        } else {
          // If the player exists, update their info
          updatedPlayers[localPlayerIndex] = {
            ...updatedPlayers[localPlayerIndex],
            name: serverPlayer.name,
            team: serverPlayer.team,
            // Always update position if it's provided from the server
            position: serverPlayer.position || updatedPlayers[localPlayerIndex].position
          };
        }
      });
    }

    // Get the current hand
    const { hand } = useGameStore.getState();

    // Remove the played card from the current player's hand if it exists there
    // This handles the case where another player played a card
    const updatedHand = hand.filter(c => c.id !== data.card.id);

    // Check if the card is already in the played cards array
    const isCardAlreadyPlayed = playedCards.some(c => c.id === data.card.id);

    if (isCardAlreadyPlayed) {
      console.warn(`Card ${data.card.value} of ${data.card.suit} is already in the played cards array. Skipping.`);
      return;
    }

    // Check if the player already has a card in the played cards array
    const playerAlreadyPlayed = playedCards.some(c => (c as any).playedBy === data.playerId);
    if (playerAlreadyPlayed) {
      console.warn(`Player ${data.playerId} already has a card in the played cards array. This might be a duplicate.`);
      // We'll still add it and let the PlayedCardsArrangement component handle deduplication
    }

    // Add the card to played cards and update players if needed
    updateGameState({
      playedCards: [...playedCards, cardWithPlayer],
      // If the current player played a card, they're no longer the current turn
      isCurrentTurn: currentTurn === socketService.getSocketId() ? false : useGameStore.getState().isCurrentTurn,
      // Update players array if we received new player info
      players: data.allPlayers && data.allPlayers.length > 0 ? updatedPlayers : players,
      // Update the hand to remove the played card
      hand: updatedHand
    });

    // Log if we removed a card from our hand
    if (updatedHand.length < hand.length) {
      console.log(`Removed card ${data.card.value} of ${data.card.suit} from local hand after it was played by ${data.playerId}`);
    }

    console.log(`Card played by ${data.playerId}, current turn: ${currentTurn}, playedCards: ${playedCards.length + 1}`);

    // Debug log to check if all played cards have the playedBy property
    const updatedPlayedCards = [...playedCards, cardWithPlayer];
    const cardsWithoutPlayedBy = updatedPlayedCards.filter(card => !(card as any).playedBy);
    if (cardsWithoutPlayedBy.length > 0) {
      console.warn('Found cards without playedBy property after update:', cardsWithoutPlayedBy);
    }

    // Log all played cards for debugging
    console.log('All played cards after update:');
    updatedPlayedCards.forEach((card, index) => {
      console.log(`Card ${index + 1}: ${card.value} of ${card.suit} played by ${(card as any).playedBy}`);
    });
  });

  // Hand completed event (when all 4 players have played a card)
  socketService.on('hand_completed', (data: {
    handId: number,
    cards: Card[],
    winner: Player,
    nextTurn: string,
    points: number,
    leadSuit: string,
    winReason: string,
    winningTeam: 1 | 2,
    team1HandsWon: number,
    team2HandsWon: number
  }) => {
    const { hands, currentHand, playedCards } = useGameStore.getState();

    console.log('Hand completed event received:', data);
    console.log('Current played cards before clearing:', playedCards.map(card =>
      `${card.value} of ${card.suit} played by ${(card as any).playedBy}`
    ));

    // Create a new hand object
    const newHand: Hand = {
      id: data.handId,
      cards: data.cards,
      leadSuit: data.leadSuit,
      winner: data.winner,
      points: data.points,
      winReason: data.winReason,
      winningTeam: data.winningTeam
    };

    // Update the game state
    updateGameState({
      hands: [...hands, newHand],
      playedCards: [], // Clear played cards for the next hand
      currentHand: currentHand + 1,
      currentTurn: data.nextTurn,
      isCurrentTurn: data.nextTurn === socketService.getSocketId()
    });

    console.log(`Hand completed. Winner: ${data.winner.name} (${data.winner.id}). Next turn: ${data.nextTurn}`);
    console.log(`Played cards cleared. Current turn set to ${data.nextTurn}`);
    console.log(`Team 1 has won ${data.team1HandsWon} hands, Team 2 has won ${data.team2HandsWon} hands`);

    // Check if this is the first or third hand won by a team (for Jordhi calling)
    const currentPlayerId = socketService.getSocketId();
    const currentPlayer = useGameStore.getState().players.find(p => p.id === currentPlayerId);

    if (currentPlayer) {
      const currentPlayerTeam = currentPlayer.team;
      const teamHandsWon = currentPlayerTeam === 1 ? data.team1HandsWon : data.team2HandsWon;

      if (teamHandsWon === 1 || teamHandsWon === 3) {
        console.log(`Your team has won ${teamHandsWon} hands. You can call Jordhi now!`);

        // Show a toast notification to inform the player they can call Jordhi
        toast.info(`Your team won ${teamHandsWon === 1 ? 'first' : 'third'} hand! You can call Jordhi now.`, {
          position: 'top-center',
          duration: 5000
        });
      }
    }

    // Check if this was the last hand of the ball (6 hands per ball)
    if (currentHand >= 6) {
      // Ball is complete, calculate points
      console.log('Ball completed, calculating points...');
    }

    // Force a re-render by updating a dummy state
    setTimeout(() => {
      updateGameState({ error: null });
    }, 100);
  });

  // Ball completed event
  socketService.on('ball_completed', (data: {
    ballId: number,
    winner: 1 | 2,
    points: {
      team1: number,
      team2: number
    },
    nextDealer: string,
    ballScores: {
      team1: number,
      team2: number
    },
    doubleProcessed?: boolean,
    thuneeSuccess?: boolean,
    thuneeFailure?: boolean,
    ballsAwarded?: number
  }) => {
    const { balls, currentBall, hands } = useGameStore.getState();

    console.log('Ball completed event received:', data);
    console.log(`Ball ${data.ballId} winner: Team ${data.winner}`);
    console.log(`Points - Team 1: ${data.points.team1}, Team 2: ${data.points.team2}`);
    console.log(`Ball Scores - Team 1: ${data.ballScores.team1}, Team 2: ${data.ballScores.team2}`);
    console.log(`Next dealer: ${data.nextDealer}`);

    if (data.doubleProcessed) {
      console.log('Double was processed for this ball');
    }
    if (data.thuneeSuccess) {
      console.log('Thunee success for this ball');
    }
    if (data.thuneeFailure) {
      console.log('Thunee failure for this ball');
    }
    if (data.ballsAwarded) {
      console.log(`Balls awarded: ${data.ballsAwarded}`);
    }

    // Create a new ball object
    const newBall: Ball = {
      id: data.ballId,
      hands: [...hands], // Copy all hands from the current ball
      winner: data.winner,
      points: data.points
    };

    // Reset the global card tracker for the new ball
    _globalCardTracker.clear();
    console.log('Cleared global card tracker for new ball');

    // Reset the last processed hand string to prevent skipping new cards in the next ball
    lastProcessedHandString = "";

    // Update the game state
    updateGameState({
      balls: [...balls, newBall],
      hands: [], // Clear hands for the next ball
      playedCards: [], // Clear played cards for the next ball
      hand: [], // Clear player's hand for the next ball
      initialHand: [], // Clear initial hand for the next ball
      currentHand: 1, // Reset hand counter
      currentBall: currentBall + 1,
      scores: data.points,
      ballScores: data.ballScores, // Update ball scores
      jordhiCalls: [], // Reset Jordhi calls for the next ball
      targetScores: { // Reset target scores to default
        team1: 105,
        team2: 105
      },
      // Update dealer
      players: useGameStore.getState().players.map(player => ({
        ...player,
        isDealer: player.id === data.nextDealer,
        // Reset trump selector status
        isTrumpSelector: false
      }))
    });

    // Force a re-render of the played cards component
    setTimeout(() => {
      const { playedCards } = useGameStore.getState();
      if (playedCards.length > 0) {
        console.log('Forcing clear of played cards');
        updateGameState({ playedCards: [] });
      }
    }, 500);

    // Show a notification to the user
    const winningTeamName = useGameStore.getState().teamNames[data.winner];
    const dealerTeam = useGameStore.getState().players.find(p => p.id === data.nextDealer)?.team;

    // Create a more detailed message that explains the dealer rotation
    let message = `Team ${winningTeamName} won the ball!`;

    // Add information about the next dealer
    const nextDealerName = useGameStore.getState().players.find(p => p.id === data.nextDealer)?.name;
    if (nextDealerName) {
      message += ` ${nextDealerName} will be the next dealer.`;
    }

    // Add explanation about dealer rotation rule
    if (dealerTeam) {
      const dealerTeamName = useGameStore.getState().teamNames[dealerTeam];
      const opposingTeamName = useGameStore.getState().teamNames[dealerTeam === 1 ? 2 : 1];

      if (data.ballScores[`team${dealerTeam}`] >= data.ballScores[`team${dealerTeam === 1 ? 2 : 1}`]) {
        message += ` (${dealerTeamName} is ahead or tied, so the deal passes to the right)`;
      } else {
        message += ` (${dealerTeamName} is behind ${opposingTeamName}, so the same dealer deals again)`;
      }
    }

    // Add information about the next steps
    const currentPlayerId = socketService.getSocketId();
    const isNextDealer = data.nextDealer === currentPlayerId;

    if (isNextDealer) {
      message += "\n\nYou are the next dealer. Please shuffle the cards.";
    } else {
      message += "\n\nWaiting for the dealer to shuffle the cards.";
    }

    const notification = {
      title: 'Ball Completed',
      message: message,
      type: 'success' as const,
      duration: 7000 // Longer duration for the more detailed message
    };

    // Dispatch notification event
    const notificationEvent = new CustomEvent('show-notification', {
      detail: notification
    });
    window.dispatchEvent(notificationEvent);
  });

  // Round completed event (legacy - keeping for compatibility)
  socketService.on('round_completed', (data: {
    roundId: number,
    cards: Card[],
    winner: Player,
    nextTurn: string
  }) => {
    const { rounds } = useGameStore.getState();
    updateGameState({
      rounds: [...rounds, { id: data.roundId, cards: data.cards, winner: data.winner }],
      playedCards: [],
      currentRound: data.roundId + 1,
      currentTurn: data.nextTurn,
      isCurrentTurn: data.nextTurn === socketService.getSocketId()
    });
  });

  // Game ended event
  socketService.on('game_ended', (data: {
    winner: 1 | 2,
    scores: { team1: number, team2: number }
  }) => {
    updateGameState({
      gameEnded: true,
      winner: data.winner,
      scores: data.scores
    });
  });

  // Error event
  socketService.on('game_error', (data: { message: string }) => {
    setError(data.message);
  });

  // Dealer found event (after dealer determination)
  socketService.on('dealer_found', (data: { dealerId: string, trumpSelectorId: string, players?: Player[] }) => {
    console.log('Dealer found:', data);

    const currentPlayerId = socketService.getSocketId();
    const { players: currentPlayers } = useGameStore.getState();

    console.log('Current players:', currentPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));
    console.log('Current player ID:', currentPlayerId);
    console.log('Dealer ID from server:', data.dealerId);

    // Use the players array from the server if available, otherwise update the existing players
    let updatedPlayers;
    if (data.players && data.players.length > 0) {
      console.log('Using players from server:', data.players.map(p => `${p.name} (${p.id}) - Team ${p.team}`));
      updatedPlayers = data.players;
    } else {
      console.log('Server did not provide players, updating existing players');
      updatedPlayers = currentPlayers.map(player => ({
        ...player,
        isDealer: player.id === data.dealerId,
        isTrumpSelector: player.id === data.trumpSelectorId
      }));
    }

    // Check if current player is dealer
    const isCurrentPlayerDealer = currentPlayerId === data.dealerId;
    console.log(`Current player (${currentPlayerId}) is dealer: ${isCurrentPlayerDealer}`);

    // Update local state
    updateGameState({
      players: updatedPlayers,
      isDealer: isCurrentPlayerDealer,
      isTrumpSelector: currentPlayerId === data.trumpSelectorId
    });

    // Log the updated state for debugging
    setTimeout(() => {
      const state = useGameStore.getState();
      console.log('Game state after dealer_found update:', {
        isDealer: state.isDealer,
        dealerId: state.players.find(p => p.isDealer)?.id,
        currentPlayerId
      });
    }, 100);
  });

  // Dealer updated event
  socketService.on('dealer_updated', (data: { dealerId: string, trumpSelectorId: string }) => {
    console.log('Dealer updated:', data);

    const { players } = useGameStore.getState();
    const currentPlayerId = socketService.getSocketId();

    console.log('Current player ID:', currentPlayerId);
    console.log('Dealer ID from server:', data.dealerId);

    // Update player roles
    const updatedPlayers = players.map(player => ({
      ...player,
      isDealer: player.id === data.dealerId,
      isTrumpSelector: player.id === data.trumpSelectorId
    }));

    // Check if current player is dealer
    const isCurrentPlayerDealer = currentPlayerId === data.dealerId;
    console.log(`Current player (${currentPlayerId}) is dealer: ${isCurrentPlayerDealer}`);

    // Update local state
    updateGameState({
      players: updatedPlayers,
      isDealer: isCurrentPlayerDealer,
      isTrumpSelector: currentPlayerId === data.trumpSelectorId
    });

    // Log the updated state for debugging
    setTimeout(() => {
      const state = useGameStore.getState();
      console.log('Game state after dealer_updated update:', {
        isDealer: state.isDealer,
        dealerId: state.players.find(p => p.isDealer)?.id,
        currentPlayerId
      });
    }, 100);
  });

  // Bidding related events

  // Bid placed event
  socketService.on('bid_placed', (data: { playerId: string, playerName: string, bid: number }) => {
    console.log(`Bid placed: ${data.playerName} bid ${data.bid}`);

    const currentPlayerId = socketService.getSocketId();
    const isCurrentPlayer = data.playerId === currentPlayerId;

    // Update the current bid and highest bidder
    updateGameState({
      currentBid: data.bid,
      highestBidder: data.playerId,
      // If this player just placed a bid, reset their turn
      canBid: isCurrentPlayer ? false : useGameStore.getState().canBid,
      isCurrentTurn: isCurrentPlayer ? false : useGameStore.getState().isCurrentTurn
    });
  });

  // Bid passed event
  socketService.on('bid_passed', (data: { playerId: string, playerName: string }) => {
    console.log(`${data.playerName} passed on bidding`);

    const currentPlayerId = socketService.getSocketId();
    const isCurrentPlayer = data.playerId === currentPlayerId;

    // If this player just passed, reset their turn
    if (isCurrentPlayer) {
      updateGameState({
        canBid: false,
        isCurrentTurn: false
      });
    }
  });

  // Your turn to bid event
  socketService.on('your_turn_to_bid', () => {
    console.log("It's your turn to bid - updating game store");

    // Update the game state to indicate it's this player's turn to bid
    updateGameState({
      canBid: true,
      isCurrentTurn: true
    });

    console.log("Game state updated, canBid set to true");
  });

  // Bidding state update event
  socketService.on('bidding_state_update', (data: {
    currentBidder: string;
    currentBidderName: string;
    currentBidderTeam: number;
    highestBid: number;
    highestBidder: string | null;
  }) => {
    console.log(`Bidding state update: Current bidder is ${data.currentBidderName} (Team ${data.currentBidderTeam})`);

    // Update the current turn in the game state
    const currentPlayerId = socketService.getSocketId();
    const isCurrentTurn = data.currentBidder === currentPlayerId;

    updateGameState({
      currentTurn: data.currentBidder,
      isCurrentTurn: isCurrentTurn,
      // Also update canBid if it's this player's turn
      canBid: isCurrentTurn ? true : useGameStore.getState().canBid,
      currentBid: data.highestBid,
      highestBidder: data.highestBidder
    });

    console.log(`Updated game state for bidding: isCurrentTurn=${isCurrentTurn}, canBid=${isCurrentTurn}`);
  });

  // Bidding complete event
  socketService.on('bidding_complete', (data: {
    trumpSelector: string,
    trumpSelectorName?: string,
    trumpSelectorTeam?: number,
    finalBid: number,
    isDefaultTrumper?: boolean
  }) => {
    console.log(`Bidding complete. Trump selector: ${data.trumpSelectorName || data.trumpSelector} (Team ${data.trumpSelectorTeam}), Final bid: ${data.finalBid}`);
    console.log(`Is default trumper: ${data.isDefaultTrumper ? 'Yes' : 'No'}`);

    // Update the game state
    const currentPlayerId = socketService.getSocketId();
    const isTrumpSelector = data.trumpSelector === currentPlayerId;
    const currentState = useGameStore.getState();

    // Find the trump selector's team
    const trumpSelector = currentState.players.find(player => player.id === data.trumpSelector);
    if (!trumpSelector) {
      console.error("Trump selector not found in players array");
      return;
    }

    const biddingTeam = trumpSelector.team as 1 | 2;
    const opposingTeam = biddingTeam === 1 ? 2 : 1;

    console.log(`Bidding team: ${biddingTeam}, Opposing team: ${opposingTeam}`);

    // Calculate the new target scores
    // If no one bid (finalBid is 0), both teams have the standard target of 105
    // Otherwise, the bidding team's target remains at 105 and the opposing team's target is reduced
    const targetScores = {
      team1: data.finalBid === 0 ? 105 : (biddingTeam === 1 ? 105 : 105 - data.finalBid),
      team2: data.finalBid === 0 ? 105 : (biddingTeam === 2 ? 105 : 105 - data.finalBid)
    };

    console.log(`New target scores: Team 1: ${targetScores.team1}, Team 2: ${targetScores.team2}`);

    // Update the players array to mark the trump selector
    const updatedPlayers = currentState.players.map(player => {
      return {
        ...player,
        isTrumpSelector: player.id === data.trumpSelector
      };
    });

    updateGameState({
      currentBid: data.finalBid,
      highestBidder: data.trumpSelector,
      biddingComplete: true,
      isTrumpSelector,
      players: updatedPlayers,
      biddingTeam,
      targetScores,
      // Reset bidding-related state
      canBid: false,
      isCurrentTurn: false
    });
  });

  // Your turn to select trump event
  socketService.on('your_turn_to_select_trump', () => {
    console.log('Your turn to select trump');
    updateGameState({
      isTrumpSelector: true
    });
  });

  // Jordhi called event (for the player who made the call)
  socketService.on('jordhi_called', (data: {
    playerId: string,
    playerName: string,
    playerTeam: 1 | 2,
    value: number,
    handNumber: number,
    targetScores: {
      team1: number,
      team2: number
    },
    isValidCards: boolean,
    isValidHandCount: boolean,
    isFullyValid: boolean,
    jordhiSuit: string | null,
    jordhiCards: Array<{ suit: string, value: string }> | []
  }) => {
    console.log(`You called a Jordhi for ${data.value} points`);
    console.log(`Is fully valid: ${data.isFullyValid}, Cards valid: ${data.isValidCards}, Hand count valid: ${data.isValidHandCount}, Suit: ${data.jordhiSuit || 'None'}`);
    if (data.jordhiCards && data.jordhiCards.length > 0) {
      console.log('Jordhi cards:', data.jordhiCards.map(c => `${c.value} of ${c.suit}`).join(', '));
    }
    console.log(`Updated target scores: Team 1: ${data.targetScores.team1}, Team 2: ${data.targetScores.team2}`);

    const { jordhiCalls } = useGameStore.getState();

    // Add the new Jordhi call to the list
    updateGameState({
      jordhiCalls: [...jordhiCalls, {
        playerId: data.playerId,
        playerName: data.playerName,
        playerTeam: data.playerTeam,
        value: data.value,
        handNumber: data.handNumber,
        isValidCards: data.isValidCards,
        isValidHandCount: data.isValidHandCount,
        isFullyValid: data.isFullyValid,
        jordhiSuit: data.jordhiSuit,
        jordhiCards: data.jordhiCards
      }],
      targetScores: data.targetScores
    });

    // Play a sound effect for the Jordhi call
    playSound(SOUNDS.JORDHI_CALL, 0.7);

    // Show a more prominent toast notification with validity information
    const { biddingTeam } = useGameStore.getState();
    const isPlayerTrumper = data.playerTeam === biddingTeam;
    const targetScoreMessage = isPlayerTrumper
      ? `Opponent team's target score increased by ${data.value} points.`
      : `Your team's target score reduced by ${data.value} points.`;

    if (data.isFullyValid) {
      toast.success(`🎮 YOUR JORDHI CALL: Your ${data.value} Jordhi call is fully valid! ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#1a2e05',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    } else if (data.isValidCards && !data.isValidHandCount) {
      toast.info(`🎮 YOUR JORDHI CALL: Your ${data.value} Jordhi call has valid cards but was made at an invalid time. ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#052e2e',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    } else {
      toast.error(`🎮 YOUR JORDHI CALL: Your ${data.value} Jordhi call is invalid! You don't have the required card combination. ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#2e1a05',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    }

    // Automatically open the Jordhi history
    updateGameState({ jordhiHistoryOpen: true });
  });

  // Jordhi called by other player event
  socketService.on('jordhi_called_by_other', (data: {
    playerId: string,
    playerName: string,
    playerTeam: 1 | 2,
    value: number,
    handNumber: number,
    targetScores: {
      team1: number,
      team2: number
    },
    isValidCards?: boolean,
    isValidHandCount?: boolean,
    isFullyValid?: boolean,
    jordhiSuit: string | null
  }) => {
    console.log(`Jordhi called by ${data.playerName} (Team ${data.playerTeam}) for ${data.value} points`);
    console.log(`Suit: ${data.jordhiSuit || 'None'}`);
    console.log(`Updated target scores: Team 1: ${data.targetScores.team1}, Team 2: ${data.targetScores.team2}`);

    if (data.isValidCards !== undefined) {
      console.log(`Validity: Cards: ${data.isValidCards}, HandCount: ${data.isValidHandCount}, FullyValid: ${data.isFullyValid}`);
    }

    const { jordhiCalls } = useGameStore.getState();

    // Add the new Jordhi call to the list with validity information if available
    updateGameState({
      jordhiCalls: [...jordhiCalls, {
        playerId: data.playerId,
        playerName: data.playerName,
        playerTeam: data.playerTeam,
        value: data.value,
        handNumber: data.handNumber,
        isValidCards: data.isValidCards,
        isValidHandCount: data.isValidHandCount,
        isFullyValid: data.isFullyValid,
        jordhiSuit: data.jordhiSuit
      }],
      targetScores: data.targetScores
    });

    // Play a sound effect for the Jordhi call
    playSound(SOUNDS.JORDHI_CALL, 0.7);

    // Show a more prominent toast notification with validity information
    const { biddingTeam, teamNames } = useGameStore.getState();
    const teamName = data.playerTeam === 1 ? teamNames[1] || 'Team 1' : teamNames[2] || 'Team 2';
    const isPlayerTrumper = data.playerTeam === biddingTeam;

    const targetScoreMessage = isPlayerTrumper
      ? `${teamName === 'Team 1' ? 'Team 2' : 'Team 1'}'s target score increased by ${data.value} points.`
      : `${teamName}'s target score reduced by ${data.value} points.`;

    // Show validity information in the toast for other players' calls
    if (data.isFullyValid) {
      toast.info(`🎮 JORDHI CALL: ${data.playerName} (${teamName}) called a valid ${data.value} Jordhi! ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#1a2e05',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    } else if (data.isValidCards && !data.isValidHandCount) {
      toast.info(`🎮 JORDHI CALL: ${data.playerName} (${teamName}) called a partially valid ${data.value} Jordhi! ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#052e2e',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    } else if (data.isValidCards === false) {
      toast.info(`🎮 JORDHI CALL: ${data.playerName} (${teamName}) called an invalid ${data.value} Jordhi! ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#2e1a05',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    } else {
      // If we don't have validity information
      toast.info(`🎮 JORDHI CALL: ${data.playerName} (${teamName}) called a ${data.value} Jordhi! ${targetScoreMessage}`, {
        position: 'top-center',
        duration: 5000,
        style: {
          backgroundColor: '#1a2e05',
          border: '2px solid #E1C760',
          padding: '16px',
          color: 'white',
          fontSize: '16px',
          fontWeight: 'bold'
        },
      });
    }

    // Automatically open the Jordhi history
    updateGameState({ jordhiHistoryOpen: true });
  });

  // Jordhi error event
  socketService.on('jordhi_error', (data: { message: string }) => {
    console.error('Jordhi error:', data.message);
    setError(data.message);

    // Show a toast notification for the error
    toast.error(`Jordhi Error: ${data.message}`, {
      position: 'top-center',
      duration: 3000
    });
  });

  // Jordhi notification event
  socketService.on('jordhi_notification', (data: { message: string }) => {
    console.log('Jordhi notification:', data.message);

    // Show a toast notification
    toast.info(data.message, {
      position: 'top-center',
      duration: 5000,
      style: {
        backgroundColor: '#1a2e05',
        border: '2px solid #E1C760',
        padding: '16px',
        color: 'white',
        fontSize: '16px',
        fontWeight: 'bold'
      },
    });
  });

  // Jordhi toast notification event
  socketService.on('jordhi_toast_notification', (data: {
    playerName: string,
    playerTeam: 1 | 2,
    value: number
  }) => {
    console.log(`Jordhi toast notification: ${data.playerName} (Team ${data.playerTeam}) called a ${data.value} Jordhi`);

    // Get team name
    const { teamNames } = useGameStore.getState();
    const teamName = data.playerTeam === 1 ? teamNames[1] || 'Team 1' : teamNames[2] || 'Team 2';

    // Show a toast notification for all players
    toast(`🎮 ${data.playerName} (${teamName}) called a ${data.value} Jordhi!`, {
      position: 'top-center',
      duration: 5000,
      style: {
        backgroundColor: '#000000',
        border: '2px solid #E1C760',
        padding: '16px',
        color: 'white',
        fontSize: '16px',
        fontWeight: 'bold'
      },
    });
  });
};

// Export the global card count function
export const getGlobalCardCount = (): number => {
  // Access the global card tracker
  return _globalCardTracker.size;
};

export default useGameStore;
